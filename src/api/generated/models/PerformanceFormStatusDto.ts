/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface PerformanceFormStatusDto
 */
export interface PerformanceFormStatusDto {
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormStatusDto
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormStatusDto
     */
    name: string;
}

/**
 * Check if a given object implements the PerformanceFormStatusDto interface.
 */
export function instanceOfPerformanceFormStatusDto(value: object): value is PerformanceFormStatusDto {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function PerformanceFormStatusDtoFromJSON(json: any): PerformanceFormStatusDto {
    return PerformanceFormStatusDtoFromJSONTyped(json, false);
}

export function PerformanceFormStatusDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): PerformanceFormStatusDto {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
    };
}

  export function PerformanceFormStatusDtoToJSON(json: any): PerformanceFormStatusDto {
      return PerformanceFormStatusDtoToJSONTyped(json, false);
  }

  export function PerformanceFormStatusDtoToJSONTyped(value?: PerformanceFormStatusDto | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

