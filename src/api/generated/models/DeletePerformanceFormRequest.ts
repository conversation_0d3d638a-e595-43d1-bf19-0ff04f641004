/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DeletePerformanceFormRequest
 */
export interface DeletePerformanceFormRequest {
    /**
     * 
     * @type {string}
     * @memberof DeletePerformanceFormRequest
     */
    reason?: string | null;
}

/**
 * Check if a given object implements the DeletePerformanceFormRequest interface.
 */
export function instanceOfDeletePerformanceFormRequest(value: object): value is DeletePerformanceFormRequest {
    return true;
}

export function DeletePerformanceFormRequestFromJSON(json: any): DeletePerformanceFormRequest {
    return DeletePerformanceFormRequestFromJSONTyped(json, false);
}

export function DeletePerformanceFormRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): DeletePerformanceFormRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'reason': json['reason'] == null ? undefined : json['reason'],
    };
}

  export function DeletePerformanceFormRequestToJSON(json: any): DeletePerformanceFormRequest {
      return DeletePerformanceFormRequestToJSONTyped(json, false);
  }

  export function DeletePerformanceFormRequestToJSONTyped(value?: DeletePerformanceFormRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'reason': value['reason'],
    };
}

