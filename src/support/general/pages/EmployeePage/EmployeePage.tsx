import { FC, ReactElement, useCallback, useEffect, useState } from 'react';

import { IllustrationMessage } from '@ot/onetalent-ui-kit';

import { EmployeeDetailModal } from './components/EmployeeDetailModal';
import { EmployeesData } from './components/EmployeesData';
import { EMPLOYEES_DATA_TABLE_COLUMN_CONFIG } from './components/EmployeesData/EmployeesDataTable/consts';
import { useAutoOpenEmployeeModal } from './hooks/useAutoOpenEmployeeModal';
import {
  SearchEmployeeUniqueId,
  useSearchEmployees
} from './hooks/useSearchEmployees';
import { useTableFilters } from './hooks/useTableFilters';

import { SearchDropdown } from '../../../shared/components/SearchDropdown';
import { useSearchInputQuery } from '../../../shared/hooks/useSearchInputQuery';

const FETCH_PAGING_SIZE = 20;

export const EmployeePage: FC = (): ReactElement => {
  const { filters, updateFilters, clearFiltersState } = useTableFilters();
  const defaultShowTable = Object.keys(filters).length > 1;

  const [totalResults, setTotalResults] = useState<number>();
  const [employees, setEmployees] = useState<SearchEmployeeUniqueId[]>();
  const [employeesFetchPage, setEmployeesFetchPage] = useState(1);
  const [selectedEmployeeId, setSelectedEmployeeId] = useState('');
  const [selectedEmployeeEmail, setSelectedEmployeeEmail] = useState('');
  const [isOpenEmployeeDetailsModal, setIsOpenEmployeeDetailsModal] =
    useState(false);
  const [showTable, setShowTable] = useState(defaultShowTable);
  const [showTableContent, setShowTableContent] = useState(true);

  const {
    selectedEmployeeIdFromUrl,
    selectedEmployeeEmailFromUrl,
    isModalOpen,
    closeModal
  } = useAutoOpenEmployeeModal();

  const { setInputValue, searchQuery, inputValue } = useSearchInputQuery(
    filters?.search?.toString()
  );

  const { data, isFetching } = useSearchEmployees({
    search: searchQuery,
    pageNumber: employeesFetchPage,
    pageSize: FETCH_PAGING_SIZE
  });

  useEffect(() => {
    const totalResultsValue = data?.paging?.totalResults;

    if (Number.isFinite(totalResultsValue)) {
      setTotalResults(totalResultsValue as number);
    }
  }, [data?.paging]);

  useEffect(() => {
    setEmployees([...(employees || []), ...(data?.items || [])]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const handleCloseModal = () => setIsOpenEmployeeDetailsModal(false);

  const finalIsModalOpen = isModalOpen ? true : isOpenEmployeeDetailsModal;
  const finalSelectedId = isModalOpen
    ? selectedEmployeeIdFromUrl
    : selectedEmployeeId;
  const finalSelectedEmail = isModalOpen
    ? selectedEmployeeEmailFromUrl
    : selectedEmployeeEmail;
  const finalSelectedCloseModal = isModalOpen ? closeModal : handleCloseModal;

  const fetchNextEmployeesBatch = useCallback(() => {
    const shouldFetchNextPage =
      Number.isFinite(totalResults) &&
      (totalResults as number) > FETCH_PAGING_SIZE * employeesFetchPage;

    if (shouldFetchNextPage) {
      setEmployeesFetchPage((prevValue) => prevValue + 1);
    }
  }, [totalResults, employeesFetchPage]);

  const resetPage = () => {
    setEmployeesFetchPage(1);
  };

  const resetEmployees = () => {
    setEmployees([]);
  };

  const handleSearchChange = useCallback(
    (searchValue: string) => {
      setInputValue(searchValue);
      updateFilters({ search: searchValue, pageNumber: null, orderBy: null });

      resetPage();
      resetEmployees();

      setShowTable(false);
    },
    [setInputValue, updateFilters]
  );

  const handleClearSearch = useCallback(() => {
    setInputValue('');
    setShowTable(false);
    clearFiltersState();
    resetPage();
    resetEmployees();
  }, [clearFiltersState, setInputValue]);

  const handleItemClick = useCallback(
    (_name: string, id: string, email: string) => {
      setSelectedEmployeeId(id);
      setSelectedEmployeeEmail(email);
      setIsOpenEmployeeDetailsModal(true);
    },
    []
  );

  const handleViewAllResults = useCallback(() => {
    setShowTable(true);
  }, []);

  const rerenderTable = useCallback(() => {
    setShowTableContent(false);

    resetPage();

    setTimeout(() => {
      setShowTableContent(true);
    });
  }, []);

  return (
    <>
      <SearchDropdown
        showEmail
        fetchNextEmployeesBatch={fetchNextEmployeesBatch}
        value={inputValue}
        onClick={handleItemClick}
        handleClearSearch={handleClearSearch}
        handleSearchChange={handleSearchChange}
        onEnter={handleViewAllResults}
        isFetching={isFetching}
        employees={employees}
        totalResults={totalResults || 0}
        onClickViewAllResults={handleViewAllResults}
        placeholderText="Search for full name, email or employee ID..."
      />

      {!showTable && (
        <div className="item-center flex h-[60dvh] justify-center bg-background--subtle p-4">
          <IllustrationMessage
            illustrationVariant="NothingFound"
            title="Find an Employee"
            description="Search using the employee's name, surname, email or ID to locate their information"
          />
        </div>
      )}

      {showTable && (
        <EmployeesData
          columnConfig={EMPLOYEES_DATA_TABLE_COLUMN_CONFIG}
          searchQuery={searchQuery}
          updateFilters={updateFilters}
          filters={filters}
          rerenderTable={rerenderTable}
          showTableContent={showTableContent}
        />
      )}

      {finalIsModalOpen && (
        <EmployeeDetailModal
          isOpen={finalIsModalOpen}
          selectedEmployeeId={finalSelectedId || ''}
          selectedEmployeeEmail={finalSelectedEmail || ''}
          onClose={finalSelectedCloseModal}
        />
      )}
    </>
  );
};
