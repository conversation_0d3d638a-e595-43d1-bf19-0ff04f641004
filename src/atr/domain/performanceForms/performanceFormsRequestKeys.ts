import { AdminService } from '@/api';

export const PERFORMANCE_FORMS_REQUEST_KEYS = {
  all: () => ['performanceForms'] as const,
  performanceFormHistory: (
    input: AdminService.AtrAdminV1PerformanceFormsPerformanceFormIdHistoryPostRequest
  ) => ['performanceFormHistory', input] as const,
  performanceFormById: (id: string) => ['performanceFormById', id] as const,
  getPerformanceForms: (payload: AdminService.PerformanceFormSearchQuery) =>
    [
      ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
      'getPerformanceForms',
      payload
    ] as const,
  getAtrFormIdSearch: (payload: AdminService.PerformanceFormSearchQuery) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getAtrFormIdSearch',
    payload
  ],
  getStatusOptions: () => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getStatusOptions'
  ],
  getTemplateNameOptions: (search?: string) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getTemplateNameOptions',
    search
  ],
  getCompanyOptions: (search?: string) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getCompanyOptions',
    search
  ],
  getDirectorateOptions: (search?: string) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getDirectorateOptions',
    search
  ],
  getFunctionOptions: (search?: string) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getFunctionOptions',
    search
  ],
  getDivisionOptions: (search?: string) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getDivisionOptions',
    search
  ],
  getAtrGroupOptions: (search?: string) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getAtrGroupOptions',
    search
  ],
  getEmployeeOptions: (search?: string) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getEmployeeOptions',
    search
  ],
  getLineManagerOptions: (search?: string) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getLineManagerOptions',
    search
  ],
  getB2BManagerOptions: (search?: string) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getB2BManagerOptions',
    search
  ],
  getDottedLineManagerOptions: (search?: string) => [
    ...PERFORMANCE_FORMS_REQUEST_KEYS.all(),
    'getDottedLineManagerOptions',
    search
  ]
} as const;
