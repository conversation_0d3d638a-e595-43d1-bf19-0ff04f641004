import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useMemo,
  useState
} from 'react';
import { useSearchParams } from 'react-router-dom';

import { FilterValue } from '@ot/onetalent-ui-kit';
import { UseQueryResult } from '@tanstack/react-query';

import { GenericDataSourceState, useDataSourceState } from 'shared/components';
import { DEFAULT_PAGE_NUMBER, DEFAULT_PAGE_SIZE } from 'shared/constants';
import {
  GenericPaginationData,
  PaginationModel
} from 'shared/core/domainModel';

import { DEFAULT_SORTING, usePerformanceForms } from '../hooks';
import {
  FilterKeys,
  PerformanceFormModel,
  PerformanceFormsSearchPayload
} from '../models';

type PerformanceFormsContext = {
  queryResult: UseQueryResult<{
    page: PaginationModel;
    items: Array<PerformanceFormModel>;
    paging: GenericPaginationData;
  }>;
  selectedFilters: Partial<Record<FilterKeys, FilterValue>>;
  dataSource: {
    dataSourceState: GenericDataSourceState<string, unknown>;
    setDataSourceState: (
      nextState: React.SetStateAction<GenericDataSourceState<string, unknown>>
    ) => void;
  };
  onSetFilter: (id: FilterKeys) => (filterValue?: FilterValue) => void;
  onSetPageSize: (pageSize: number) => void;
  onClearFilters: () => void;
};

export const PerformanceFormsContext = createContext<PerformanceFormsContext>(
  undefined!
);

export const usePerformanceFormsContext = () =>
  useContext(PerformanceFormsContext);

export const PerformanceFormsContextProvider: FC<PropsWithChildren> = ({
  children
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [selectedFilters, setSelectedFilters] = useState<
    Partial<Record<FilterKeys, FilterValue>>
  >(
    searchParams.entries().reduce(
      (acc, [filterId, filterValues]) => ({
        ...acc,
        [filterId]: filterValues.split(',')
      }),
      {}
    )
  );

  const { dataSource, parameters: pagination } = useDataSourceState<string>({
    pageSize,
    initialPageNumber: DEFAULT_PAGE_NUMBER,
    filters: selectedFilters
  });

  const queryPayload = useMemo<PerformanceFormsSearchPayload>(
    () =>
      Object.entries(selectedFilters).reduce(
        (accumulator, [id, filter]) => ({
          ...accumulator,
          [id]: filter
        }),
        {}
      ),
    [selectedFilters]
  );

  const queryResult = usePerformanceForms({
    ...queryPayload,
    ...pagination,
    orderBy: DEFAULT_SORTING
  });

  const handleClearFilters = useCallback(() => {
    setSearchParams(
      (currentSearchParams) => {
        Object.keys(selectedFilters).forEach((filterKey) => {
          currentSearchParams.delete(filterKey);
        });

        return currentSearchParams;
      },
      { replace: true }
    );
    setSelectedFilters({});
  }, [selectedFilters, setSearchParams]);

  const handleSetFilter = useCallback(
    (id: FilterKeys) => (filterValue?: FilterValue) => {
      setSelectedFilters((currentSelectedFilters) => {
        if (filterValue) {
          return {
            ...currentSelectedFilters,
            [id]: filterValue
          };
        }
        const newSelectedFilters = { ...currentSelectedFilters };
        delete newSelectedFilters[id];
        return newSelectedFilters;
      });

      setSearchParams(
        (currentSearchParams) => {
          if (!filterValue) {
            currentSearchParams.delete(id);
          } else {
            currentSearchParams.set(id, filterValue.map((id) => id).toString());
          }

          return currentSearchParams;
        },
        { replace: true }
      );
    },
    [setSearchParams]
  );

  const contextValue = useMemo(
    () => ({
      dataSource,
      queryResult,
      selectedFilters,
      onSetPageSize: setPageSize,
      onSetFilter: handleSetFilter,
      onClearFilters: handleClearFilters
    }),
    [
      dataSource,
      handleClearFilters,
      handleSetFilter,
      queryResult,
      selectedFilters
    ]
  );

  return (
    <PerformanceFormsContext.Provider value={contextValue}>
      {children}
    </PerformanceFormsContext.Provider>
  );
};
