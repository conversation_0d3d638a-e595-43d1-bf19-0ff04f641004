import { useMemo, useState } from 'react';

import { Link, Text } from '@ot/onetalent-ui-kit';

import { BulkActionsFormInformer } from '@/atr/components/BulkActions/BulkActionsFormInformer';
import { BulkActionsFormLoader } from '@/atr/components/BulkActions/BulkActionsFormLoader';
import { BulkActionsFormProcessorButtons } from '@/atr/components/BulkActions/BulkActionsFormProcessorButtons';
import { useMyFileCancelProcessing, useMyFileProcessing } from '@/atr/domain';
import { useGetMyFilesPolling } from '@/atr/domain/files/hooks/useGetMyFilesPolling';
import {
  isActionStatusCancelling,
  isActionStatusPendingOrInProgress
} from '@/atr/domain/files/utils';
import { useActionTypeQueryParam } from '@/atr/hooks/actionType';
import { useMyFilesActions } from '@/atr/providers';

import { BulkActionsCancelProcessModal } from './BulkActionsCancelProcessModal';

export const BulkActionsFormProcessor = () => {
  const { myFile } = useMyFilesActions();
  const { actionType } = useActionTypeQueryParam();
  const { mutate: processMyFile, isPending: isPendingProcess } =
    useMyFileProcessing(actionType);
  const [isCancelModalVisible, setIsCancelModalVisible] = useState(false);
  const changeModalVisibility = () => {
    setIsCancelModalVisible((state) => !state);
  };
  const { mutate: cancelProcessing, isPending: isPendingCancelProcess } =
    useMyFileCancelProcessing({
      onSuccess: changeModalVisibility
    });

  const cancelProcess = () => {
    myFile?.id && cancelProcessing({ actionId: myFile.id, type: actionType });
  };

  const isCancellingProcess = useMemo(
    () => isActionStatusCancelling(myFile?.status) || isPendingCancelProcess,
    [isPendingCancelProcess, myFile?.status]
  );
  const isProcessing = useMemo(
    () =>
      (isActionStatusPendingOrInProgress(myFile?.status) || isPendingProcess) &&
      !isCancellingProcess,
    [isCancellingProcess, isPendingProcess, myFile?.status]
  );

  useGetMyFilesPolling();

  return (
    <>
      <BulkActionsFormInformer />
      {!(isProcessing || isCancellingProcess) ? (
        <BulkActionsFormProcessorButtons processMyFile={processMyFile} />
      ) : (
        <div className="flex grow items-center">
          <BulkActionsFormLoader>
            <Text className="text-center text-header-3-medium">
              Please wait,
              <br /> your {isCancellingProcess ? 'action' : 'file'} is being{' '}
              {isCancellingProcess
                ? 'canceled'
                : myFile?.isValidation
                ? 'validated'
                : 'imported'}
              ...
              {!isCancellingProcess && (
                <Link className="mx-auto mt-24" onClick={changeModalVisibility}>
                  Cancel
                </Link>
              )}
            </Text>
          </BulkActionsFormLoader>
        </div>
      )}
      <BulkActionsCancelProcessModal
        confirmCb={cancelProcess}
        isOpen={isCancelModalVisible}
        changeModalVisibility={changeModalVisibility}
        text="By cancelling the action, the rows that have already been completed will
                  remain processed and cannot be reverted. Only the remaining rows in the
                  file will be skipped. After the cancellation, you can view the results in
                  the output file."
      />
    </>
  );
};
