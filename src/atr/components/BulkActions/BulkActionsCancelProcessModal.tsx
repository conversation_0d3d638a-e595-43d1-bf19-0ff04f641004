import { FC } from 'react';

import {
  But<PERSON>,
  ButtonSize,
  ButtonVarian<PERSON>,
  Informer,
  InformerVariant,
  Modal
} from '@ot/onetalent-ui-kit';

interface BulkActionsCancelProcessModalProps {
  confirmCb: () => void;
  isOpen: boolean;
  changeModalVisibility: () => void;
  text: string;
}

export const BulkActionsCancelProcessModal: FC<
  BulkActionsCancelProcessModalProps
> = ({ confirmCb, isOpen, changeModalVisibility, text }) => {
  const handleCancel = () => {
    confirmCb();
  };

  return (
    <Modal
      header="Confirm Changes"
      isOpen={isOpen}
      footer={
        <>
          <Button
            size={ButtonSize.Medium}
            variant={ButtonVariant.Secondary}
            onClick={changeModalVisibility}
          >
            Discard
          </Button>
          <Button
            size={ButtonSize.Medium}
            variant={ButtonVariant.Primary}
            onClick={handleCancel}
          >
            Confirm
          </Button>
        </>
      }
      onClose={changeModalVisibility}
    >
      <Informer
        variant={InformerVariant.Info}
        title="Are you sure you want to cancel the action?"
        description={text}
      />
    </Modal>
  );
};
