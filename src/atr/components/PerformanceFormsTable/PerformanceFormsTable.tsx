import { FC, useState, useCallback } from 'react';

import { usePerformanceFormsContext } from 'atr/domain';

import { PerformanceDataTable } from '@/shared/components';
import { DEFAULT_PAGE_SIZES } from '@/shared/constants/pagination';

import {
  DeleteFormModal,
  PerformanceFormsEmpty,
  PerformanceFormsError
} from './components';
import { useColumnGroups, useColumns } from './hooks';

export interface PerformanceFormsTableProps {
  emptyScreenSubtitle?: string;
  className?: string;
}

export const PerformanceFormsTable: FC<PerformanceFormsTableProps> = ({
  className
}) => {
  const { queryResult, dataSource, onSetPageSize } =
    usePerformanceFormsContext();

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedFormId, setSelectedFormId] = useState<string | null>(null);

  const handleDeleteForm = useCallback((formId: string) => {
    setSelectedFormId(formId);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
    setSelectedFormId(null);
  }, []);

  const columns = useColumns({ onDeleteForm: handleDeleteForm });
  const columnGroups = useColumnGroups();

  return (
    <>
      <div data-attributes="PerformanceFormsTable" className={className}>
        <PerformanceDataTable
          dataAttributes="PerformanceFormsTable"
          {...dataSource}
          showContainer
          styles={{
            minHeight: !queryResult.data?.items.length ? 440 : 0
          }}
          queryResult={queryResult}
          columns={columns}
          columnGroups={columnGroups}
          pageSizes={DEFAULT_PAGE_SIZES}
          setPageSize={onSetPageSize}
          renderNoResults={PerformanceFormsEmpty}
          renderError={PerformanceFormsError}
          loaderText="ATR Forms are being loaded"
          paginationSize="24"
        />
      </div>

      <DeleteFormModal
        isOpen={isDeleteModalOpen}
        formId={selectedFormId}
        onClose={handleCloseDeleteModal}
        onSuccess={handleCloseDeleteModal}
      />
    </>
  );
};
