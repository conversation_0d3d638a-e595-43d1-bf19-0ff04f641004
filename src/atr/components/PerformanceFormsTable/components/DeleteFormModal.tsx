import { FC, useState, useCallback } from 'react';

import {
  Button,
  ButtonSize,
  ButtonVariant,
  Informer,
  InformerVariant,
  Modal,
  Textarea
} from '@ot/onetalent-ui-kit';

import { useDeletePerformanceForm } from '@/atr/domain';

export interface DeleteFormModalProps {
  isOpen: boolean;
  formId?: string | null;
  onSuccess?: () => void;
  onClose: () => void;
}

const MAX_REASON_LENGTH = 250;

export const DeleteFormModal: FC<DeleteFormModalProps> = ({
  isOpen,
  formId,
  onSuccess,
  onClose
}) => {
  const [reason, setReason] = useState('');
  const isReasonEmpty = !reason.trim();

  const handleClose = useCallback(() => {
    setReason('');
    onClose();
  }, [onClose]);

  const { mutate: deleteForm, isPending } = useDeletePerformanceForm({
    onSuccess: () => {
      onSuccess?.();
      handleClose();
    }
  });

  const handleConfirmDelete = useCallback(() => {
    if (!formId || isReasonEmpty) {
      return;
    }

    deleteForm({
      performanceFormId: formId,
      reason: reason.trim()
    });
  }, [formId, isReasonEmpty, deleteForm, reason]);

  if (!formId) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      header="Delete ATR Form"
      footer={
        <div className="flex justify-end gap-16">
          <Button
            variant={ButtonVariant.Secondary}
            size={ButtonSize.Medium}
            onClick={handleClose}
            disabled={isPending}
          >
            Discard
          </Button>

          <Button
            variant={ButtonVariant.Primary}
            size={ButtonSize.Medium}
            disabled={isReasonEmpty || isPending}
            onClick={handleConfirmDelete}
            tooltip={{
              title: isReasonEmpty ? 'Please specify the reason' : null
            }}
          >
            Confirm
          </Button>
        </div>
      }
    >
      <div className="flex flex-col gap-24">
        <Informer
          variant={InformerVariant.Error}
          title="Are you sure you want to delete the form?"
          description="You will not be able to restore the form after the deletion, but a new ATR form can be created if needed."
        />

        <Textarea
          label="Specify Reason"
          required
          rows={3}
          value={reason}
          disabled={isPending}
          maxLength={MAX_REASON_LENGTH}
          onChange={(e) => setReason(e.target.value)}
        />
      </div>
    </Modal>
  );
};
