import { TagVariant } from '@ot/onetalent-ui-kit';

import { ATR_FORM_MAJOR_STATUSES } from '@/atr/domain';

export const getTagVariantByMajorStatus = (majorStatus: string): TagVariant => {
  switch (majorStatus) {
    case ATR_FORM_MAJOR_STATUSES.SELF_ASSESSMENT:
      return TagVariant.LightBlue;
    case ATR_FORM_MAJOR_STATUSES.MANAGER_ASSESSMENT:
      return TagVariant.Supernova;
    case ATR_FORM_MAJOR_STATUSES.DOTTED_LINE_MANAGER_ENDORSEMENT:
      return TagVariant.Electric;
    case ATR_FORM_MAJOR_STATUSES.RATING_APPROVAL:
      return TagVariant.PacificBlue;
    case ATR_FORM_MAJOR_STATUSES.COMPLETED:
      return TagVariant.Green;
    default:
      return TagVariant.Grey;
  }
};
