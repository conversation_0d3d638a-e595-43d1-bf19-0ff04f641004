import { ForwardedRef, forwardRef } from 'react';
import { FieldValues } from 'react-hook-form';

import { Textarea, TextareaProps } from './Textarea';

import { HookFormConnector, HookFormFieldCommonProps } from '../HookForm';

export type TextareaRHFProps<TFieldValues extends FieldValues> = TextareaProps &
  HookFormFieldCommonProps<TFieldValues>;

function TextareaRHFComponent<TFieldValues extends FieldValues>(
  props: TextareaRHFProps<TFieldValues>,
  ref: ForwardedRef<HTMLTextAreaElement>
) {
  const { name, control, rules, ...inputProps } = props;

  return (
    <HookFormConnector
      ref={ref}
      name={name}
      rules={rules}
      control={control}
      renderField={(fieldProps) => <Textarea {...inputProps} {...fieldProps} />}
    />
  );
}

/** Textarea with React Hook Form validation */
export const TextareaRHF = forwardRef(TextareaRHFComponent) as <
  TFieldValues extends FieldValues
>(
  props: TextareaRHFProps<TFieldValues>
) => ReturnType<typeof TextareaRHFComponent>;
